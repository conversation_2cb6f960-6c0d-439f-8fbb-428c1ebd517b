<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\ClientErrorLog;
use Illuminate\Http\Request;

class ClientLoggingController extends Controller
{
    /**
     * Store client error log
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'user_id' => 'nullable|string|exists:users,id',
                'company_id' => 'nullable|string|exists:companies,id',
                'device_id' => 'required|string',
                'error_code' => 'required|string',
                'error_message' => 'required|string',
                'stack_trace' => 'required|string',
                'data' => 'nullable|json'
            ]);

            $log = ClientErrorLog::create($validated);

            return response()->json([
                'success' => true,
                'message' => 'Error logged successfully',
                'log_id' => $log->id
            ], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Server error',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
