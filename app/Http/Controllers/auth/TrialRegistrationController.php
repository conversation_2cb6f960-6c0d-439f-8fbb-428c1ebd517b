<?php

namespace App\Http\Controllers\auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserDetail;
use App\Models\Company;
use App\Models\Receipt;
use App\Models\Subscription\SubscriptionPlan;
use App\Models\Subscription\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TrialRegistrationController extends Controller
{
    /**
     * Show the trial registration form
     */
    public function index()
    {
        return view('backend.auth.trial-registration');
    }

    /**
     * Validate a specific step of the registration form
     */
    public function validateStep(Request $request)
    {
        $step = $request->input('step');
        $rules = $this->getValidationRules($step);
        
        $validator = Validator::make($request->all(), $rules);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ]);
        }
        
        return response()->json(['success' => true]);
    }

    /**
     * Check username availability
     */
    public function checkUsername(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|unique:users,username'
        ]);

        return response()->json([
            'available' => !$validator->fails(),
            'message' => $validator->fails() ? 'Username cannot be used' : 'Username available'
        ]);
    }

    /**
     * Process the trial registration
     */
    public function processTrialRegistration(Request $request)
    {
        // Validate all steps
        $validator = Validator::make($request->all(), $this->getAllValidationRules());
        
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // Find the trial plan
            $trialPlan = SubscriptionPlan::where('name', 'Starter - Trial')
                ->where('is_active', true)
                ->first();

            if (!$trialPlan) {
                throw new \Exception('Trial plan not found');
            }

            // Create the user
            $user = User::create([
                'username' => $request->username,
                'email' => $request->email,
                'password' => Hash::make($request->password)
            ]);

            // Create user details
            $userDetail = UserDetail::create([
                'user_id' => $user->id,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'mobile' => $request->mobile,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'postcode' => $request->postcode,
                'country' => $request->country
            ]);

            // Create company
            $company = Company::create([
                'user_id' => $user->id,
                'com_name' => $request->business_name,
                'com_address' => $request->business_address,
                'com_city' => $request->business_city,
                'com_state' => $request->business_state,
                'com_postcode' => $request->business_postcode,
                'com_country' => $request->business_country,
                'com_registration_no' => $request->business_registration_no,
                'com_mobile' => $request->mobile,
                'com_email' => $request->email,
                'com_sst_value' => '0'
            ]);

            // Create receipt
            $receipt = Receipt::create([
                'company_id' => $company->id,
                'user_id' => $user->id,
                'title' => $request->business_name,
                'name' => $request->first_name . ' ' . $request->last_name,
                'email' => $request->email,
                'phone' => $request->mobile,
                'address' => $request->business_address,
                'city' => $request->business_city,
                'state' => $request->business_state,
                'postcode' => $request->business_postcode,
                'country' => $request->business_country,
                'sst' => '0,0',
            ]);

            // Create trial subscription
            $subscription = Subscription::create([
                'company_id' => $company->id,
                'subscription_plan_id' => $trialPlan->id,
                'starts_at' => now(),
                'ends_at' => now()->addDays(14),
                'trial_ends_at' => now()->addDays(14),
                'status' => 'active',
                'bill_type' => 'trial'
            ]);

            DB::commit();

            // Log the user in
            auth()->login($user);

            Log::info('Trial registration completed', [
                'user_id' => $user->id,
                'company_id' => $company->id,
                'subscription_id' => $subscription->id
            ]);

            return redirect()->route('dashboard')->with('success', 
                'Welcome! Your 14-day free trial has started. Explore all features and see how BizappOS can help your business grow.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Trial registration failed', [
                'error' => $e->getMessage(),
                'email' => $request->email
            ]);

            return redirect()->back()
                ->withInput()
                ->withErrors(['general' => 'Registration failed. Please try again.']);
        }
    }

    /**
     * Get validation rules for a specific step
     */
    private function getValidationRules($step)
    {
        switch ($step) {
            case 1:
                return [
                    'username' => 'required|unique:users,username|min:3|max:50',
                    'first_name' => 'required|string|max:100',
                    'last_name' => 'required|string|max:100',
                    'email' => 'required|email|unique:users,email',
                    'mobile' => 'required|string|min:10|max:15',
                    'password' => 'required|min:8',
                    'password_confirmation' => 'required|same:password'
                ];
            case 2:
                return [
                    'address' => 'required|string|max:255',
                    'city' => 'required|string|max:100',
                    'state' => 'required|string|max:100',
                    'postcode' => 'required|string|max:10',
                    'country' => 'required|string|max:100'
                ];
            case 3:
                return [
                    'business_name' => 'required|string|max:255',
                    'business_registration_no' => 'required|string|max:50',
                    'business_address' => 'required|string|max:255',
                    'business_city' => 'required|string|max:100',
                    'business_state' => 'required|string|max:100',
                    'business_postcode' => 'required|string|max:10',
                    'business_country' => 'required|string|max:100'
                ];
            default:
                return [];
        }
    }

    /**
     * Get all validation rules for final submission
     */
    private function getAllValidationRules()
    {
        return array_merge(
            $this->getValidationRules(1),
            $this->getValidationRules(2),
            $this->getValidationRules(3)
        );
    }
}
