body {
    background-color: #fff;
  }
  #auth {
    height: 100vh;
    overflow-x: hidden;
  }
  #auth #auth-right {
    background: url(../../../mazer/images/4853433.png?45649b87e0b3f50bfa1372c6cdb4595f),
      linear-gradient(90deg, #7262a8, #aca4d4);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  #auth #auth-left {
    padding: 3rem 4rem;
    overflow-y: auto;
    max-height: 100vh;
  }
  #auth #auth-left .auth-title {
    font-size: 4rem;
    margin-bottom: 1rem;
  }
  #auth #auth-left .auth-subtitle {
    color: #a8aebb;
    font-size: 1.7rem;
    line-height: 2.5rem;
  }
  #auth #auth-left .auth-logo {
    margin-bottom: 5rem;
  }
  /* #auth #auth-left .auth-logo img {
    height: 2rem;
  } */
  /* Responsive adjustments */
  @media screen and (max-width: 991px) {
    #auth #auth-left {
      padding: 3rem 2rem;
    }
    #auth #auth-left .auth-title {
      font-size: 3rem;
    }
  }
  @media screen and (max-width: 767px) {
    #auth #auth-left {
      padding: 2rem 1.5rem;
    }
    #auth #auth-left .auth-title {
      font-size: 2.5rem;
    }
  }
  @media screen and (max-width: 576px) {
    #auth #auth-left {
      padding: 1.5rem 1rem;
    }
    #auth #auth-left .auth-title {
      font-size: 2rem;
    }
  }
  /* Ensure proper 50/50 split on larger screens */
  @media screen and (min-width: 992px) {
    #auth .row > .col-lg-6:first-child {
      flex: 0 0 50%;
      max-width: 50%;
    }
    #auth .row > .col-lg-6:last-child {
      flex: 0 0 50%;
      max-width: 50%;
    }
  }

  /* Improve form spacing for trial registration */
  .trial-wizard .form-group {
    margin-bottom: 1.5rem;
  }

  .trial-wizard .step-indicator {
    margin-bottom: 2rem;
  }

  /* Better mobile experience */
  @media screen and (max-width: 991px) {
    #auth #auth-left {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }

  body.theme-dark #auth-right {
    background: url(../../../mazer/images/4853433.png?45649b87e0b3f50bfa1372c6cdb4595f),
      linear-gradient(90deg, #2d499d, #3f5491);
  }
  