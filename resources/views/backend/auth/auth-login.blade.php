<!DOCTYPE html>
<html lang="en">
  <head>
    @section('title', 'Login')

    @include('./backend/partials/head')
    <link rel="stylesheet" href="mazer/css/pages/auth.css" />
    <link rel="stylesheet" href="mazer/extensions/sweetalert2/sweetalert2.min.css"/>

  </head>

  <body>
    <div id="auth">
      <div class="row h-100 g-0">

        <div class="col-lg-6 d-none d-lg-block">
          <div id="auth-right">
            <img src="mazer/images/logo/pos_logo_long.png" class="img-fluid text-center px-5" alt="Logo"/>
          </div>
        </div>

        <div class="col-lg-6 col-12">
          <div id="auth-left">
            <div class="auth-logo">
              {{-- <a href="{{ route('dashboard') }}"
                ><img src="mazer/images/logo/pos_logo_long.png" class="img-fluid" alt="Logo"
              /></a> --}}
            </div>
            <h1 class="auth-title">Sign In</h1>
            <p class="auth-subtitle mb-5">
              Sign In with your data that you entered during registration.
            </p>

            <div class="col-md-12 mb-5">
              @include('backend.partials.flash-message')
            </div>

            {{-- consider untuk tambah tab ( bizapp user (default) / non-bizapp user )   --}}
            <form action="{{ route('login.process') }}" method="POST" id="loginForm">
              @csrf

              <div class="form-group position-relative has-icon-left mb-4">
                <input
                  type="text"
                  class="form-control form-control-xl"
                  placeholder="Username"
                  name="username"
                />
                @if ($errors->has('username'))
                <span class="text-danger">{{ $errors->first('username') }}</span>
                @endif

                <div class="form-control-icon">
                  <i class="bi bi-person"></i>
                </div>
              </div>
              <div class="form-group position-relative has-icon-left mb-4">
                <div class="input-group">

                <input
                  type="password"
                  class="form-control form-control-xl"
                  placeholder="Password"
                  name="password"
                  id="password"
                  style="padding-right: 3rem;"
                />
   
                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                  <i class="bi bi-eye-slash"></i> 
                </button>               

                @if ($errors->has('password'))
                <span class="text-danger">{{ $errors->first('password') }}</span>
                @endif

                <div class="form-control-icon">
                  <i class="bi bi-shield-lock"></i>
                </div>
              </div>

              </div>
              <div class="form-group position-relative has-icon-left mb-4 d-none" id="domain-field">
                <input
                  type="text"
                  class="form-control form-control-xl text-uppercase"
                  placeholder="DOMAIN"
                  name="domain"
                />
                @if ($errors->has('domain'))
                <span class="text-danger">{{ $errors->first('domain') }}</span>
                @endif

                <div class="form-control-icon">
                  <i class="bi bi-hdd-stack"></i>
                </div>
              </div>


              {{-- <div class="form-check form-check-lg d-flex align-items-end">
                <input
                  class="form-check-input me-2"
                  type="checkbox"
                  name="checkbox"
                  value="1"
                  id="remember"
                />
                <label
                  class="form-check-label text-gray-600"
                  for="remember"
                >
                  Keep me logged in
                </label>
              </div> --}}
              <div class="form-check form-check-lg d-flex align-items-end">
                <input
                  class="form-check-input me-2"
                  type="checkbox"
                  name="domain_checkbox"
                  value="1"
                  id="has_domain"
                />
                <label
                  class="form-check-label text-gray-600"
                  for="has_domain"
                >
                  Bizapp ULTIMATE user?
                </label>
              </div>

              <button class="btn btn-primary btn-block btn-lg shadow-lg mt-5">
                Log in
              </button>
            </form>
             <div class="text-center mt-5 text-lg fs-4">
              <p class="text-gray-600">
                Don't have an account?
                <a href="{{ route('register') }}" class="font-bold">Sign up</a>.
              </p>
              <p class="text-gray-600">
                Or try our <a href="{{ route('trial.register') }}" class="font-bold text-primary">14-day free trial</a>
              </p>
              <p>
                <a class="font-bold" href="{{ route('login.forgot') }}"
                  >Forgot password?</a>
              </p>
            </div>
          </div>
        </div>

      </div>
    </div>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="mazer/js/bootstrap.js"></script>
    <script src="mazer/js/app.js"></script>
    {{-- <script src="mazer/extensions/sweetalert2/sweetalert2.min.js"></script>
    <script src="mazer/js/pages/sweetalert2.js"></script> --}}

    {{-- <script>
    @if(session('error'))
        Swal.fire(
          'ERROR',
          '{{Session::get('error')}}',
        );
      @elseif (session('success'))
      Swal.fire(
          'SUCCESS',
          '{{Session::get('success')}}',
        );
    @endif
      </script> --}}

    <script>
      // checkbox for domain
      let box = document.getElementById("domain-field");
      // Add event listener to checkbox
      let checkbox = document.getElementById("has_domain");
      checkbox.addEventListener("click", function() {
        // Get the checkbox
        var checkBox = document.getElementById("has_domain");
        if (checkBox.checked == true) {
          //When checked:
          box.classList.remove("d-none");
        } else {
          //When unchecked:
          box.classList.add("d-none");
        }
      });

      document.getElementById('togglePassword').addEventListener('click', function (e) {
        const passwordInput = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (passwordInput.type === 'password') {
          passwordInput.type = 'text';
          icon.classList.remove('bi-eye-slash');
          icon.classList.add('bi-eye');
        } else {
          passwordInput.type = 'password';
          icon.classList.remove('bi-eye');
          icon.classList.add('bi-eye-slash');
        }
      });

      // refresh csrf token before submit to ensure no more 419 errors
      document.getElementById('loginForm').addEventListener('submit', function(e) {
      e.preventDefault();
      const form = this;

      fetch('/refresh-csrf')
          .then(response => {
              if (!response.ok) throw new Error('Network error');
              return response.json();
          })
          .then(data => {
              form.querySelector('input[name="_token"]').value = data.token;
              form.submit();
          })
          .catch(error => {
              console.error('CSRF Refresh Failed:', error);
              // Optionally show error to user
              alert('Session refresh failed. Please reload page and try again.');
          });
  });

    </script>
  </body>
</html>
