<!-- Username -->
<div class="form-group position-relative has-icon-left mb-4">
    <div class="input-group">
        <input
            type="text"
            class="form-control form-control-xl"
            placeholder="Username"
            name="username"
            id="username"
            value="{{ old('username') }}"
            required
        />
        <button type="button" class="btn btn-primary" id="checkUsernameBtn">
            Check
        </button>
        <div class="form-control-icon">
            <i class="bi bi-person"></i>
        </div>
    </div>
    <div id="usernameStatus" class="mt-2"></div>
    @if ($errors->has('username'))
        <span class="text-danger">{{ $errors->first('username') }}</span>
    @endif
</div>

<!-- First Name -->
<div class="form-group position-relative has-icon-left mb-4">
    <input
        type="text"
        class="form-control form-control-xl"
        placeholder="First Name"
        name="first_name"
        id="first_name"
        value="{{ old('first_name') }}"
        required
    />
    <div class="form-control-icon">
        <i class="bi bi-person-badge"></i>
    </div>
    @if ($errors->has('first_name'))
        <span class="text-danger">{{ $errors->first('first_name') }}</span>
    @endif
</div>

<!-- Last Name -->
<div class="form-group position-relative has-icon-left mb-4">
    <input
        type="text"
        class="form-control form-control-xl"
        placeholder="Last Name"
        name="last_name"
        id="last_name"
        value="{{ old('last_name') }}"
        required
    />
    <div class="form-control-icon">
        <i class="bi bi-person-badge-fill"></i>
    </div>
    @if ($errors->has('last_name'))
        <span class="text-danger">{{ $errors->first('last_name') }}</span>
    @endif
</div>

<!-- Email -->
<div class="form-group position-relative has-icon-left mb-4">
    <input
        type="email"
        class="form-control form-control-xl"
        placeholder="Email Address"
        name="email"
        id="email"
        value="{{ old('email') }}"
        required
    />
    <div class="form-control-icon">
        <i class="bi bi-envelope"></i>
    </div>
    @if ($errors->has('email'))
        <span class="text-danger">{{ $errors->first('email') }}</span>
    @endif
</div>

<!-- Mobile -->
<div class="form-group position-relative has-icon-left mb-4">
    <input
        inputmode="numeric"
        oninput="this.value = this.value.replace(/\D+/g, '')"
        class="form-control form-control-xl"
        placeholder="Mobile Number"
        name="mobile"
        id="mobile"
        value="{{ old('mobile') }}"
        required
    />
    <div class="form-control-icon">
        <i class="bi bi-phone"></i>
    </div>
    @if ($errors->has('mobile'))
        <span class="text-danger">{{ $errors->first('mobile') }}</span>
    @endif
</div>

<!-- Password -->
<div class="form-group position-relative has-icon-left mb-4">
    <input
        type="password"
        class="form-control form-control-xl"
        placeholder="Password (minimum 8 characters)"
        name="password"
        id="password"
        required
    />
    <div class="form-control-icon">
        <i class="bi bi-shield-lock"></i>
    </div>
    <button type="button" class="btn btn-light btn-sm position-absolute" id="togglePassword1"
        style="right: 15px; top: 10px;">
        <i class="bi bi-eye-slash" id="toggleIcon1"></i>
    </button>
    @if ($errors->has('password'))
        <span class="text-danger">{{ $errors->first('password') }}</span>
    @endif
</div>

<!-- Confirm Password -->
<div class="form-group position-relative has-icon-left mb-4">
    <input
        type="password"
        class="form-control form-control-xl"
        placeholder="Confirm Password"
        name="password_confirmation"
        id="password_confirmation"
        required
    />
    <div class="form-control-icon">
        <i class="bi bi-shield-check"></i>
    </div>
    <button type="button" class="btn btn-light btn-sm position-absolute" id="togglePassword2"
        style="right: 15px; top: 10px;">
        <i class="bi bi-eye-slash" id="toggleIcon2"></i>
    </button>
    @if ($errors->has('password_confirmation'))
        <span class="text-danger">{{ $errors->first('password_confirmation') }}</span>
    @endif
</div>

<script>
// Username availability check
document.getElementById('checkUsernameBtn').addEventListener('click', function() {
    const username = document.getElementById('username').value;
    const statusElement = document.getElementById('usernameStatus');

    if (!username) {
        statusElement.innerHTML = '<span class="text-danger">Please enter a username</span>';
        return;
    }

    fetch('{{ route("trial.register.check.username") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({ username: username })
    })
    .then(response => response.json())
    .then(data => {
        if (data.available) {
            statusElement.innerHTML = '<span class="text-success"><i class="bi bi-check-circle"></i> ' + data.message + '</span>';
        } else {
            statusElement.innerHTML = '<span class="text-danger"><i class="bi bi-x-circle"></i> ' + data.message + '</span>';
        }
    })
    .catch(error => {
        statusElement.innerHTML = '<span class="text-danger">Error checking username</span>';
    });
});

// Clear username status on input change
document.getElementById('username').addEventListener('input', function() {
    document.getElementById('usernameStatus').innerHTML = '';
});

// Password toggle functionality
document.getElementById('togglePassword1').addEventListener('click', function() {
    const password = document.getElementById('password');
    const icon = document.getElementById('toggleIcon1');
    
    if (password.type === 'password') {
        password.type = 'text';
        icon.classList.remove('bi-eye-slash');
        icon.classList.add('bi-eye');
    } else {
        password.type = 'password';
        icon.classList.remove('bi-eye');
        icon.classList.add('bi-eye-slash');
    }
});

document.getElementById('togglePassword2').addEventListener('click', function() {
    const password = document.getElementById('password_confirmation');
    const icon = document.getElementById('toggleIcon2');
    
    if (password.type === 'password') {
        password.type = 'text';
        icon.classList.remove('bi-eye-slash');
        icon.classList.add('bi-eye');
    } else {
        password.type = 'password';
        icon.classList.remove('bi-eye');
        icon.classList.add('bi-eye-slash');
    }
});
</script>
