<!-- Address -->
<div class="form-group position-relative has-icon-left mb-4">
    <textarea
        class="form-control form-control-xl"
        placeholder="Your Address"
        name="address"
        id="address"
        rows="3"
        required
    >{{ old('address') }}</textarea>
    <div class="form-control-icon">
        <i class="bi bi-geo-alt"></i>
    </div>
    @if ($errors->has('address'))
        <span class="text-danger">{{ $errors->first('address') }}</span>
    @endif
</div>

<!-- City -->
<div class="form-group position-relative has-icon-left mb-4">
    <input
        type="text"
        class="form-control form-control-xl"
        placeholder="City"
        name="city"
        id="city"
        value="{{ old('city') }}"
        required
    />
    <div class="form-control-icon">
        <i class="bi bi-building"></i>
    </div>
    @if ($errors->has('city'))
        <span class="text-danger">{{ $errors->first('city') }}</span>
    @endif
</div>

<!-- State -->
<div class="form-group position-relative has-icon-left mb-4">
    <select class="form-control form-control-xl" name="state" id="state" required>
        <option value="">Select State</option>
        <option value="Johor" {{ old('state') == 'Johor' ? 'selected' : '' }}>Johor</option>
        <option value="Kedah" {{ old('state') == 'Kedah' ? 'selected' : '' }}>Kedah</option>
        <option value="Kelantan" {{ old('state') == 'Kelantan' ? 'selected' : '' }}>Kelantan</option>
        <option value="Kuala Lumpur" {{ old('state') == 'Kuala Lumpur' ? 'selected' : '' }}>Kuala Lumpur</option>
        <option value="Labuan" {{ old('state') == 'Labuan' ? 'selected' : '' }}>Labuan</option>
        <option value="Melaka" {{ old('state') == 'Melaka' ? 'selected' : '' }}>Melaka</option>
        <option value="Negeri Sembilan" {{ old('state') == 'Negeri Sembilan' ? 'selected' : '' }}>Negeri Sembilan</option>
        <option value="Pahang" {{ old('state') == 'Pahang' ? 'selected' : '' }}>Pahang</option>
        <option value="Penang" {{ old('state') == 'Penang' ? 'selected' : '' }}>Penang</option>
        <option value="Perak" {{ old('state') == 'Perak' ? 'selected' : '' }}>Perak</option>
        <option value="Perlis" {{ old('state') == 'Perlis' ? 'selected' : '' }}>Perlis</option>
        <option value="Putrajaya" {{ old('state') == 'Putrajaya' ? 'selected' : '' }}>Putrajaya</option>
        <option value="Sabah" {{ old('state') == 'Sabah' ? 'selected' : '' }}>Sabah</option>
        <option value="Sarawak" {{ old('state') == 'Sarawak' ? 'selected' : '' }}>Sarawak</option>
        <option value="Selangor" {{ old('state') == 'Selangor' ? 'selected' : '' }}>Selangor</option>
        <option value="Terengganu" {{ old('state') == 'Terengganu' ? 'selected' : '' }}>Terengganu</option>
    </select>
    <div class="form-control-icon">
        <i class="bi bi-map"></i>
    </div>
    @if ($errors->has('state'))
        <span class="text-danger">{{ $errors->first('state') }}</span>
    @endif
</div>

<!-- Postcode -->
<div class="form-group position-relative has-icon-left mb-4">
    <input
        type="text"
        inputmode="numeric"
        oninput="this.value = this.value.replace(/\D+/g, '')"
        class="form-control form-control-xl"
        placeholder="Postcode"
        name="postcode"
        id="postcode"
        value="{{ old('postcode') }}"
        maxlength="5"
        required
    />
    <div class="form-control-icon">
        <i class="bi bi-mailbox"></i>
    </div>
    @if ($errors->has('postcode'))
        <span class="text-danger">{{ $errors->first('postcode') }}</span>
    @endif
</div>

<!-- Country -->
<div class="form-group position-relative has-icon-left mb-4">
    <select class="form-control form-control-xl" name="country" id="country" required>
        <option value="Malaysia" {{ old('country', 'Malaysia') == 'Malaysia' ? 'selected' : '' }}>Malaysia</option>
    </select>
    <div class="form-control-icon">
        <i class="bi bi-globe"></i>
    </div>
    @if ($errors->has('country'))
        <span class="text-danger">{{ $errors->first('country') }}</span>
    @endif
</div>


