<!-- Business Name -->
<div class="trial-form-group trial-has-icon">
    <input
        type="text"
        class="trial-form-control form-control-xl"
        placeholder="Business Name"
        name="business_name"
        id="business_name"
        value="{{ old('business_name') }}"
        required
    />
    <div class="trial-form-icon">
        <i class="bi bi-shop"></i>
    </div>
    @if ($errors->has('business_name'))
        <span class="text-danger">{{ $errors->first('business_name') }}</span>
    @endif
</div>

<!-- Business Registration Number (SSM) -->
<div class="trial-form-group trial-has-icon">
    <input
        type="text"
        class="trial-form-control form-control-xl"
        placeholder="Business Registration Number (SSM)"
        name="business_registration_no"
        id="business_registration_no"
        value="{{ old('business_registration_no') }}"
        required
    />
    <div class="trial-form-icon">
        <i class="bi bi-card-text"></i>
    </div>
    <small class="text-muted">Enter your SSM registration number (e.g., 123456-A, SA0123456-X)</small>
    @if ($errors->has('business_registration_no'))
        <span class="text-danger">{{ $errors->first('business_registration_no') }}</span>
    @endif
</div>

<!-- Business Address -->
<div class="trial-form-group trial-has-icon">
    <textarea
        class="trial-form-textarea form-control-xl"
        placeholder="Business Address"
        name="business_address"
        id="business_address"
        rows="3"
        required
    >{{ old('business_address') }}</textarea>
    <div class="trial-form-icon">
        <i class="bi bi-building"></i>
    </div>
    @if ($errors->has('business_address'))
        <span class="text-danger">{{ $errors->first('business_address') }}</span>
    @endif
</div>

<!-- Business City -->
<div class="trial-form-group trial-has-icon">
    <input
        type="text"
        class="trial-form-control form-control-xl"
        placeholder="Business City"
        name="business_city"
        id="business_city"
        value="{{ old('business_city') }}"
        required
    />
    <div class="trial-form-icon">
        <i class="bi bi-geo-alt-fill"></i>
    </div>
    @if ($errors->has('business_city'))
        <span class="text-danger">{{ $errors->first('business_city') }}</span>
    @endif
</div>

<!-- Business State -->
<div class="trial-form-group trial-has-icon">
    <select class="trial-form-select form-control-xl" name="business_state" id="business_state" required>
        <option value="" disabled {{ old('business_state') ? '' : 'selected' }}>Select Business State</option>
        <option value="Johor" {{ old('business_state') == 'Johor' ? 'selected' : '' }}>Johor</option>
        <option value="Kedah" {{ old('business_state') == 'Kedah' ? 'selected' : '' }}>Kedah</option>
        <option value="Kelantan" {{ old('business_state') == 'Kelantan' ? 'selected' : '' }}>Kelantan</option>
        <option value="Kuala Lumpur" {{ old('business_state') == 'Kuala Lumpur' ? 'selected' : '' }}>Kuala Lumpur</option>
        <option value="Labuan" {{ old('business_state') == 'Labuan' ? 'selected' : '' }}>Labuan</option>
        <option value="Melaka" {{ old('business_state') == 'Melaka' ? 'selected' : '' }}>Melaka</option>
        <option value="Negeri Sembilan" {{ old('business_state') == 'Negeri Sembilan' ? 'selected' : '' }}>Negeri Sembilan</option>
        <option value="Pahang" {{ old('business_state') == 'Pahang' ? 'selected' : '' }}>Pahang</option>
        <option value="Penang" {{ old('business_state') == 'Penang' ? 'selected' : '' }}>Penang</option>
        <option value="Perak" {{ old('business_state') == 'Perak' ? 'selected' : '' }}>Perak</option>
        <option value="Perlis" {{ old('business_state') == 'Perlis' ? 'selected' : '' }}>Perlis</option>
        <option value="Putrajaya" {{ old('business_state') == 'Putrajaya' ? 'selected' : '' }}>Putrajaya</option>
        <option value="Sabah" {{ old('business_state') == 'Sabah' ? 'selected' : '' }}>Sabah</option>
        <option value="Sarawak" {{ old('business_state') == 'Sarawak' ? 'selected' : '' }}>Sarawak</option>
        <option value="Selangor" {{ old('business_state') == 'Selangor' ? 'selected' : '' }}>Selangor</option>
        <option value="Terengganu" {{ old('business_state') == 'Terengganu' ? 'selected' : '' }}>Terengganu</option>
    </select>
    <div class="trial-form-icon">
        <i class="bi bi-map-fill"></i>
    </div>
    @if ($errors->has('business_state'))
        <span class="text-danger">{{ $errors->first('business_state') }}</span>
    @endif
</div>

<!-- Business Postcode -->
<div class="trial-form-group trial-has-icon">
    <input
        type="text"
        inputmode="numeric"
        oninput="this.value = this.value.replace(/\D+/g, '')"
        class="trial-form-control form-control-xl"
        placeholder="Business Postcode"
        name="business_postcode"
        id="business_postcode"
        value="{{ old('business_postcode') }}"
        maxlength="5"
        required
    />
    <div class="trial-form-icon">
        <i class="bi bi-mailbox2"></i>
    </div>
    @if ($errors->has('business_postcode'))
        <span class="text-danger">{{ $errors->first('business_postcode') }}</span>
    @endif
</div>

<!-- Business Country -->
<div class="trial-form-group trial-has-icon">
    <select class="trial-form-select form-control-xl" name="business_country" id="business_country" required>
        <option value="Malaysia" {{ old('business_country', 'Malaysia') == 'Malaysia' ? 'selected' : '' }}>Malaysia</option>
    </select>
    <div class="trial-form-icon">
        <i class="bi bi-globe2"></i>
    </div>
    @if ($errors->has('business_country'))
        <span class="text-danger">{{ $errors->first('business_country') }}</span>
    @endif
</div>

<!-- Copy Address Button -->
<div class="form-group mb-4">
    <div class="form-check">
        <input class="form-check-input" type="checkbox" id="copyPersonalAddress">
        <label class="form-check-label" for="copyPersonalAddress">
            Same as personal address
        </label>
    </div>
</div>

<script>
// Copy personal address to business address
document.getElementById('copyPersonalAddress').addEventListener('change', function() {
    if (this.checked) {
        // Get personal address values from previous steps
        const personalAddress = document.getElementById('address').value;
        const personalCity = document.getElementById('city').value;
        const personalState = document.getElementById('state').value;
        const personalPostcode = document.getElementById('postcode').value;
        const personalCountry = document.getElementById('country').value;

        // Set business address values
        document.getElementById('business_address').value = personalAddress;
        document.getElementById('business_city').value = personalCity;
        document.getElementById('business_state').value = personalState;
        document.getElementById('business_postcode').value = personalPostcode;
        document.getElementById('business_country').value = personalCountry;
    } else {
        // Clear business address values
        document.getElementById('business_address').value = '';
        document.getElementById('business_city').value = '';
        document.getElementById('business_state').value = '';
        document.getElementById('business_postcode').value = '';
        document.getElementById('business_country').value = 'Malaysia'; // Reset to default
    }
});
</script>
