<!-- Trial Benefits -->
<div class="trial-benefits">
    <h5><i class="bi bi-gift text-success"></i> What's Included in Your 14-Day Free Trial</h5>
    <ul>
        <li><i class="bi bi-check-circle text-success"></i> Full access to all Starter plan features</li>
        <li><i class="bi bi-check-circle text-success"></i> Up to 50 product SKUs</li>
        <li><i class="bi bi-check-circle text-success"></i> 1 staff account</li>
        <li><i class="bi bi-check-circle text-success"></i> Point of Sale system</li>
        <li><i class="bi bi-check-circle text-success"></i> Inventory management</li>
        <li><i class="bi bi-check-circle text-success"></i> Sales reporting</li>
        <li><i class="bi bi-check-circle text-success"></i> Customer management</li>
        <li><i class="bi bi-check-circle text-success"></i> No credit card required</li>
    </ul>
</div>

<!-- Review Information -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-person-circle"></i> Personal Information</h6>
            </div>
            <div class="card-body">
                <div class="review-item">
                    <strong>Name:</strong>
                    <span id="review-name"></span>
                </div>
                <div class="review-item">
                    <strong>Username:</strong>
                    <span id="review-username"></span>
                </div>
                <div class="review-item">
                    <strong>Email:</strong>
                    <span id="review-email"></span>
                </div>
                <div class="review-item">
                    <strong>Mobile:</strong>
                    <span id="review-mobile"></span>
                </div>
                <div class="review-item">
                    <strong>Address:</strong>
                    <span id="review-address"></span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-building"></i> Business Information</h6>
            </div>
            <div class="card-body">
                <div class="review-item">
                    <strong>Business Name:</strong>
                    <span id="review-business-name"></span>
                </div>
                <div class="review-item">
                    <strong>Registration No:</strong>
                    <span id="review-business-registration"></span>
                </div>
                <div class="review-item">
                    <strong>Business Address:</strong>
                    <span id="review-business-address"></span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Trial Information -->
<div class="alert alert-info mt-4">
    <h6><i class="bi bi-info-circle"></i> Trial Information</h6>
    <p class="mb-2"><strong>Trial Duration:</strong> 14 days from registration</p>
    <p class="mb-2"><strong>Trial Start:</strong> Immediately after registration</p>
    <p class="mb-0"><strong>What happens after trial:</strong> You can choose to upgrade to a paid plan or your account will be deactivated</p>
</div>

<!-- Terms and Conditions -->
<div class="form-group mb-4">
    <div class="form-check">
        <input class="form-check-input" type="checkbox" id="agreeTerms" name="agree_terms" required>
        <label class="form-check-label" for="agreeTerms">
            I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
        </label>
    </div>
    @if ($errors->has('agree_terms'))
        <span class="text-danger">{{ $errors->first('agree_terms') }}</span>
    @endif
</div>

<!-- Marketing Consent -->
{{-- <div class="form-group mb-4">
    <div class="form-check">
        <input class="form-check-input" type="checkbox" id="marketingConsent" name="marketing_consent">
        <label class="form-check-label" for="marketingConsent">
            I would like to receive updates about new features and promotions (optional)
        </label>
    </div>
</div> --}}

<style>
.review-item {
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #f1f1f1;
}
.review-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}
.review-item strong {
    display: inline-block;
    width: 120px;
    color: #6c757d;
    font-size: 0.9rem;
}
.review-item span {
    color: #495057;
    font-weight: 500;
}
.card {
    border: 1px solid #e9ecef;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 1rem;
}
.card-header h6 {
    color: #495057;
    font-weight: 600;
}
</style>

<script>
// Populate review information when step 4 is shown
function populateReviewInformation() {
    // Personal Information
    const firstName = document.getElementById('first_name').value;
    const lastName = document.getElementById('last_name').value;
    const username = document.getElementById('username').value;
    const email = document.getElementById('email').value;
    const mobile = document.getElementById('mobile').value;
    const address = document.getElementById('address').value;
    const city = document.getElementById('city').value;
    const state = document.getElementById('state').value;
    const postcode = document.getElementById('postcode').value;
    const country = document.getElementById('country').value;
    
    // Business Information
    const businessName = document.getElementById('business_name').value;
    const businessRegistration = document.getElementById('business_registration_no').value;
    const businessAddress = document.getElementById('business_address').value;
    const businessCity = document.getElementById('business_city').value;
    const businessState = document.getElementById('business_state').value;
    const businessPostcode = document.getElementById('business_postcode').value;
    const businessCountry = document.getElementById('business_country').value;
    
    // Update review fields
    document.getElementById('review-name').textContent = `${firstName} ${lastName}`;
    document.getElementById('review-username').textContent = username;
    document.getElementById('review-email').textContent = email;
    document.getElementById('review-mobile').textContent = mobile;
    document.getElementById('review-address').textContent = `${address}, ${city}, ${state} ${postcode}, ${country}`;
    
    document.getElementById('review-business-name').textContent = businessName;
    document.getElementById('review-business-registration').textContent = businessRegistration;
    document.getElementById('review-business-address').textContent = `${businessAddress}, ${businessCity}, ${businessState} ${businessPostcode}, ${businessCountry}`;
}

// Call this function when step 4 becomes active
document.addEventListener('DOMContentLoaded', function() {
    // This will be called by the main wizard script when step 4 is shown
    window.populateReviewInformation = populateReviewInformation;
});
</script>
